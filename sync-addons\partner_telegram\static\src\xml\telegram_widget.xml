<?xml version="1.0" encoding="UTF-8" ?>
<!-- Copyright 2024-2025 <PERSON> <https://twitter.com/yelizariev>
     License MIT (https://opensource.org/licenses/MIT). -->
<templates xml:space="preserve">
    <t t-name="partner_contact.TelegramField" owl="1">
        <t t-if="props.readonly">
            <div class="d-grid">
                <a
                    class="o_form_uri o_text_overflow"
                    t-on-click.stop=""
                    t-att-href="props.record.data.telegram_url"
                    t-esc="props.value || ''"
                />
            </div>
        </t>
        <t t-else="">
            <div class="d-inline-flex w-100">
                <input
                    class="o_input"
                    t-att-id="props.id"
                    type="text"
                    autocomplete="off"
                    t-att-placeholder="props.placeholder"
                    t-att-required="props.required"
                    t-ref="input"
                />
            </div>
        </t>
    </t>
    <t
        t-name="partner_contact.FormTelegramField"
        t-inherit="partner_contact.TelegramField"
        t-inherit-mode="primary"
    >
        <xpath expr="//input" position="after">
            <a
                t-if="props.record.data[props.name]"
                t-att-href="props.record.data.telegram_url"
                class="ms-3 d-inline-flex align-items-center"
            >
                <i
                    class="fa fa-telegram"
                    data-tooltip="Open in Telegram"
                    aria-label="Open in Telegram"
                />
            </a>
        </xpath>
    </t>
</templates>
