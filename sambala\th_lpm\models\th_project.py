from odoo import models, fields, api
import json

class ThProject(models.Model):
    _name = 'th.project.lpm'
    _description = "Dự án"
    _inherit = ["mail.thread", "mail.activity.mixin"]

    th_project_code = fields.Char(string="Mã dự án", required=True)
    name = fields.Char(string="Tên", required=True)
    th_description = fields.Text(string="Mô tả")
    th_project_scope = fields.Text(string="Phạm vi dự án")
    th_implementation_unit = fields.Char(string="Đơn vị thực hiện")
    th_lead_the_project = fields.Many2one('res.users', string="Chủ trì dự án")
    th_project_members = fields.Many2many('res.users', string="Thành viên dự án")
    th_start_date = fields.Date(string="Ngày bắt đầu")
    th_end_date = fields.Date(string="Ngày kết thúc")
    th_university_id = fields.Many2one('th.origin', string="Trường")
    th_catalog_warehouse_id = fields.Many2one('th.catalog.warehouse', string="Danh mục sản xuất")
    th_product_manufacturing_ids = fields.One2many('th.product.manufacturing', 'th_project_lpm_id', string="môn")
    th_catalog_warehouse_Selection = fields.Selection(selection='_get_warehouse_selection',
                                                       string="Danh mục sản xuất",
                                                       group_expand='_group_expand_states')
    th_total_proposed_costs = fields.Float(string='Tổng chi phí đề xuất theo tín chỉ', readonly=True)
    th_total_object_costs = fields.Float(string='Tổng chi phí đề xuất theo môn',readonly=True)
    th_total_production_costs = fields.Float(string="Tổng chi phí sản xuất theo TCSX (Dự kiến)", compute="_th_total_production_costs")
    th_production_number = fields.Integer(string="Số lượng môn",readonly=True)
    th_cost_qa = fields.Float(string='Chi Phí QA', compute='_compute_th_costs_incurred')
    th_percent_cost_qa = fields.Float(string='Phần trăm chi phí QA', default='30')
    th_percent_costs_incurred = fields.Float(string='Phần trăm chi phí phát sinh', default='10')
    th_costs_incurred = fields.Float(string='Chi phí phát sinh')
    th_selection_costs = fields.Selection(
        selection=[('proposed', 'Theo tín chỉ'),
                   ('object', 'Theo môn')], string="Chi phí theo")
    th_approve = fields.Boolean(string="Đã được phê duyệt")
    th_is_lpm2 = fields.Boolean(string="Đã chuyển sang LPM2", default=False)
    readonly_domain = fields.Char(compute="_compute_readonly_domain")
    th_team_id = fields.Many2one('th.team', string='Đội nhóm')
    th_lead_domain = fields.Char(compute='_compute_lead_domain', string='Domain chủ trì')
    th_members_domain = fields.Char(compute='_compute_members_domain', string='Domain thành viên')

    # domain cho chủ trì dự án dựa trên đội nhóm đã chọn
    @api.depends('th_team_id')
    def _compute_lead_domain(self):
        for rec in self:
            if rec.th_team_id:
                # Lấy tất cả trưởng nhóm và thành viên trong đội nhóm
                user_ids = []
                if rec.th_team_id.th_manager_id:
                    user_ids.append(rec.th_team_id.th_manager_id.id)
                if rec.th_team_id.th_user_ids:
                    user_ids.extend(rec.th_team_id.th_user_ids.ids)
                # Loại bỏ trùng lặp
                user_ids = list(set(user_ids))
                domain = [('id', 'in', user_ids)]
            else:
                domain = []
            rec.th_lead_domain = json.dumps(domain)

    # Tính domain cho thành viên dự án dựa trên đội nhóm và chủ trì đã chọn
    @api.depends('th_team_id', 'th_lead_the_project')
    def _compute_members_domain(self):
        for rec in self:
            if rec.th_team_id:
                # Lấy tất cả trưởng nhóm và thành viên trong đội nhóm
                user_ids = []
                if rec.th_team_id.th_manager_id:
                    user_ids.append(rec.th_team_id.th_manager_id.id)
                if rec.th_team_id.th_user_ids:
                    user_ids.extend(rec.th_team_id.th_user_ids.ids)
                # Loại bỏ trùng lặp
                user_ids = list(set(user_ids))
                # Loại bỏ chủ trì đã chọn
                if rec.th_lead_the_project:
                    user_ids = [uid for uid in user_ids if uid != rec.th_lead_the_project.id]
                domain = [('id', 'in', user_ids)]
            else:
                domain = []
            rec.th_members_domain = json.dumps(domain)

    @api.onchange('th_team_id')
    def _onchange_th_team_id(self):
        """Khi chọn đội nhóm, reset chủ trì và thành viên dự án"""
        # Reset chủ trì và thành viên khi đổi đội nhóm
        self.th_lead_the_project = False
        self.th_project_members = [(5, 0, 0)]

    @api.onchange('th_lead_the_project')
    def _onchange_th_lead_the_project(self):
        """Khi chọn chủ trì, reset thành viên dự án"""
        if self.th_lead_the_project:
            # Reset thành viên dự án khi đổi chủ trì
            self.th_project_members = [(5, 0, 0)]

    def _compute_readonly_domain(self):
        for rec in self:
            if rec.th_approve == True:
                rec.readonly_domain = json.dumps([])
            else:
                rec.readonly_domain = False
    @api.depends('th_selection_costs', 'th_percent_cost_qa', 'th_percent_costs_incurred')
    def _compute_th_costs_incurred(self):
        for rec in self:
            if rec.th_selection_costs == 'proposed' and rec.th_total_proposed_costs > 0:
                rec.th_costs_incurred = (rec.th_total_proposed_costs / 100) * rec.th_percent_costs_incurred
                rec.th_cost_qa = (rec.th_total_proposed_costs / 100) * rec.th_percent_cost_qa
            elif rec.th_selection_costs == 'object' and rec.th_total_object_costs > 0:
                rec.th_costs_incurred = (rec.th_total_object_costs / 100) * rec.th_percent_costs_incurred
                rec.th_cost_qa = (rec.th_total_object_costs / 100) * rec.th_percent_cost_qa
            else:
                rec.th_costs_incurred = 0
                rec.th_cost_qa = 0
    @api.model
    def _get_warehouse_selection(self):
        warehouses = self.env['th.catalog.warehouse'].search([])
        return [(str(warehouse.id), warehouse.name) for warehouse in warehouses]
    @api.model
    def _group_expand_states(self, states, domain, order):
        warehouses = self.env['th.catalog.warehouse'].search([])
        state = [(str(warehouse.id), warehouse.name) for warehouse in warehouses]
        return [key for key, val in state]

    def action_th_product_manufacturing(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm.th_product_manufacturing_action")
        return action

    # @api.model
    # def _th_total_production_costs(self):
    #     for rec in self:
    #         cost = 0
    #         th_total_object_costs = 0
    #         th_total_proposed_costs = 0
    #         th_production_number = 0
    #         if rec.th_product_manufacturing_ids:
    #             for record in rec.th_product_manufacturing_ids:
    #                 cost += record.th_standard_costs
    #                 th_total_object_costs += record.th_object_costs
    #                 th_total_proposed_costs += record.th_proposed_costs
    #                 th_production_number = len(rec.th_product_manufacturing_ids.ids)
    #         rec.th_total_production_costs = cost
    #         rec.th_total_object_costs = th_total_object_costs
    #         rec.th_total_proposed_costs = th_total_proposed_costs
    #         rec.th_production_number = th_production_number
    @api.model
    def _th_total_production_costs(self):
        for rec in self:
            rec.th_total_production_costs = sum(rec.th_product_manufacturing_ids.mapped('th_standard_costs'))
            rec.th_total_object_costs = sum(rec.th_product_manufacturing_ids.mapped('th_object_costs'))
            rec.th_total_proposed_costs = sum(rec.th_product_manufacturing_ids.mapped('th_proposed_costs'))
            rec.th_production_number = len(rec.th_product_manufacturing_ids)