exclude: |
  (?x)
  # Files and folders generated by bots, to avoid loops
  ^setup/|/static/description/index\.html$|/i18n/.*\.pot?$|
  # Maybe reactivate this when all README files include prettier ignore tags?
  ^README\.md$|
  # Library files can have extraneous formatting (even minimized)
  /lib/|
  # Repos using Sphinx to generate docs don't need prettying
  ^docs/_templates/.*\.html$|
  # You don't usually want a bot to modify your legal texts
  (LICENSE.*|COPYING.*)
default_language_version:
  python: python3
  node: "14.13.0"
repos:
  - repo: https://github.com/myint/autoflake
    rev: v1.6.1
    hooks:
      - id: autoflake
        args: ["-i", "--ignore-init-module-imports"]
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.4.1
    hooks:
      - id: prettier
        name: prettier + plugin-xml
        additional_dependencies:
          - "prettier@2.1.2"
          - "@prettier/plugin-xml@0.12.0"
        args:
          - --plugin=@prettier/plugin-xml
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.24.0
    hooks:
      - id: eslint
        verbose: true
        args:
          - --color
          - --fix
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: trailing-whitespace
        # exclude autogenerated files
        exclude: /README\.rst$
      - id: end-of-file-fixer
        # exclude autogenerated files
        exclude: /README\.rst$
      - id: debug-statements
      - id: fix-encoding-pragma
        args: ["--remove"]
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-xml
      - id: mixed-line-ending
        args: ["--fix=lf"]
  - repo: https://github.com/OCA/pylint-odoo
    rev: 7.0.2
    hooks:
      - id: pylint_odoo
        name: pylint with optional checks
        args:
          - --rcfile=.pylintrc
          - --exit-zero
        verbose: true
      - id: pylint_odoo
        args:
          - --rcfile=.pylintrc-mandatory
  - repo: https://github.com/asottile/pyupgrade
    rev: v2.29.0
    hooks:
      - id: pyupgrade
        args: ["--keep-percent-format"]
  #- repo: https://github.com/acsone/setuptools-odoo
  #  rev: 3.1.5
  #  hooks:
  #    - id: setuptools-odoo-make-default
  #    - id: setuptools-odoo-get-requirements
  #      args:
  #        - --output
  #        - requirements.txt
  #        - --header
  #        - "# generated from manifests external_dependencies"
  - repo: https://github.com/pycqa/flake8
    rev: 3.9.2
    hooks:
      - id: flake8
        name: flake8
        additional_dependencies: ["flake8-bugbear==21.9.2"]
  - repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort except __init__.py
        args:
          - --settings=.
        exclude: /__init__\.py$
