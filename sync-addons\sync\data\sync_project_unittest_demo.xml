<?xml version="1.0" encoding="UTF-8" ?>
<!-- Copyright 2020,2024 <PERSON> <https://twitter.com/yelizariev>
     Copyright 2021 <PERSON> <https://github.com/troji<PERSON>>
     Copyright 2021 <PERSON><PERSON> <https://github.com/mentalko>
     License MIT (https://opensource.org/licenses/MIT). -->
<odoo>
    <!-- unittest -->
    <record id="test_project" model="sync.project">
        <field name="name">Project For Unittests</field>
    </record>
    <record id="test_project_param" model="sync.project.param">
        <field name="key">PARTNER_RELATION</field>
        <field name="initial_value">sync_unittest_partner</field>
        <field name="project_id" ref="sync.test_project" />
    </record>
    <record id="test_task" model="sync.task">
        <field name="name">Assign ref to new partners</field>
        <field name="technical_name">test</field>
        <field name="project_id" ref="sync.test_project" />
        <field name="code">
<![CDATA[
def handle_db(records):
    for r in records:
        r.set_link(PARAMS.PARTNER_RELATION, r.id)
]]>
        </field>
    </record>
    <record id="test_trigger_automation" model="sync.trigger.automation">
        <field name="name">Sync test: Run code On partner created</field>
        <field name="trigger_name">ON_PARTNER_CREATED</field>
        <field name="sync_task_id" ref="sync.test_task" />
        <field name="model_id" ref="base.model_res_partner" />
        <field name="trigger">on_create</field>
    </record>
</odoo>
