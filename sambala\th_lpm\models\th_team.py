from odoo import models, fields, api
from odoo.exceptions import ValidationError
import json


class ThUserApprove(models.Model):
    _name = 'th.team'
    _description = "Đội nhóm"

    name = fields.Char(string="Tên đội nhóm")
    th_user_ids = fields.Many2many('res.users', string="Thành viên dự án")
    th_manager_id = fields.Many2one('res.users', string="Trưởng nhóm")
    th_members_domain = fields.Char(compute='_compute_members_domain', string='Domain thành viên')

    # Tính domain để loại trừ trưởng nhóm khỏi danh sách thành viên
    @api.depends('th_manager_id')
    def _compute_members_domain(self):
        for rec in self:
            if rec.th_manager_id:
                domain = [('id', '!=', rec.th_manager_id.id)]
            else:
                domain = []
            rec.th_members_domain = json.dumps(domain)

    # Kiểm tra trưởng nhóm không được là thành viên của chính đội nhóm đó
    @api.constrains('th_manager_id', 'th_user_ids')
    def _th_check_manager_not_member(self):
        for rec in self:
            if rec.th_manager_id and rec.th_manager_id in rec.th_user_ids:
                raise ValidationError("Trưởng nhóm không được chọn làm thành viên dự án.")

    # check không cho trùng tên đội nhóm
    @api.constrains('name')
    def _th_check_unique_team_name(self):
        for rec in self:
            if self.search_count([('name', '=', rec.name)]) > 1:
                raise ValidationError("Tên đội nhóm phải là duy nhất.")
