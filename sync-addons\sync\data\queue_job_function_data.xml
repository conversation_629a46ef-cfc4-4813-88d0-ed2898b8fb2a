<?xml version="1.0" encoding="UTF-8" ?>
<!-- Copyright 2020 <PERSON> <https://twitter.com/yelizariev>
     License MIT (https://opensource.org/licenses/MIT). -->
<odoo>
    <record id="queue_job_function_task_run" model="queue.job.function">
        <field name="model_id" ref="sync.model_sync_task" />
        <field name="method">run</field>
        <field
            name="retry_pattern"
            eval="{1: 5 * 60, 2: 15 * 60, 3: 60 * 60, 4: 3 * 60 * 60}"
        />
    </record>
</odoo>
