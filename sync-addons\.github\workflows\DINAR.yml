# Copyright 2020 IT Projects Labs
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: "DINAR: Docker Image Building"

on:
  push:
    paths:
      - ".DINAR/**"
      - ".github/workflows/DINAR.yml"

  # Cron works only for defaul branch. See https://github.com/itpp-labs/DINAR/issues/48
  schedule:
    - cron: "5 5 * * 0"

jobs:
  check-secret:
    runs-on: ubuntu-latest
    steps:
      - name: Check that DINAR_TOKEN is set
        run: |
          if [ -z "${{ secrets.DINAR_TOKEN }}" ]
          then
              echo "DINAR_TOKEN is not set"
              exit 1
          fi

  check-branch:
    runs-on: ubuntu-latest
    steps:
      - name: Check that this branch needs docker images
        run: |
          REF=${GITHUB_BASE_REF:-${GITHUB_REF}}
          BRANCH=${REF##*/}
          CHECK=$( echo "$BRANCH" | grep -E "^(master|[0-9]+\.[0-9]+)(-dev-.+)?$" || true)
          if [ -z "$CHECK" ]
          then
              echo "This branch is not supposed to be a target of pull requests, so docker image is not needed."
              echo "For information check https://github.com/itpp-labs/DINAR/issues/60"
              exit 1
          fi

  rebuild-images:
    runs-on: ubuntu-latest
    needs:
      - check-secret
      - check-branch
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          path: REPO
      - name: Checkout DINAR
        uses: actions/checkout@v4
        with:
          path: DINAR
          repository: itpp-labs/DINAR-fork
          ref: master
      - uses: actions/setup-python@v4
        with:
          python-version: "3.10"
      - name: Prepare build folder
        run: |
          cp -rnT DINAR/embedded-files/ REPO/

      - name: Configure Docker
        run: |
          bash DINAR/workflow-files/configure-docker.sh ${{ secrets.DINAR_TOKEN }}

          cat <<- EOF > REPO/.DINAR/image/.netrc
          machine github.com
          login $GITHUB_ACTOR
          password ${{ secrets.DINAR_TOKEN }}
          EOF
      - name: Build ${{ env.IMAGE_ODOO_BASE }}
        uses: elgohr/Publish-Docker-Github-Action@v4
        env:
          LOCAL_CUSTOM_DIR: ./image
          AGGREGATE: true
          PIP_INSTALL_ODOO: false
          CLEAN: false
          COMPILE: false
        with:
          name: ${{ env.IMAGE_ODOO_BASE }}
          registry: ${{ env.REGISTRY }}
          username: ${{ env.REGISTRY_USERNAME }}
          password: ${{ env.REGISTRY_PASSWORD }}
          buildargs: ODOO_VERSION,AGGREGATE,PIP_INSTALL_ODOO,CLEAN,COMPILE,LOCAL_CUSTOM_DIR
          workdir: REPO/.DINAR/
      - name: Install python tools
        run: |
          pip install plumbum pyyaml
      - name: Compute Modules Dependencies
        run: |
          # sets environment variables that available in next steps via $ {{ env.VAR_NAME }} notation
          cd REPO
          python ../DINAR/workflow-files/analyze-modules.py all
      - name: Install Base Addons
        run: |
          export MODULES=$ALL_MODULES_DEPENDENCIES

          export DOODBA_WITHOUT_DEMO=all
          bash DINAR/workflow-files/images-with-preinstalled-modules.sh $IMAGE_DB-nodemo $IMAGE_ODOO-nodemo

          export DOODBA_WITHOUT_DEMO=false
          bash DINAR/workflow-files/images-with-preinstalled-modules.sh $IMAGE_DB $IMAGE_ODOO
