# Copyright 2020 IT Projects Labs
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: "Port module"

on:
  issues:
    types:
      - opened
      - reopened

jobs:
  port:
    runs-on: ubuntu-latest
    if: "startsWith(github.event.issue.title, 'DINAR-PORT ')"
    steps:
      - name: Post link
        uses: KeisukeYamashita/create-comment@v1
        with:
          comment:
            "Porting is started. Check logs: https://github.com/${{ github.repository
            }}/actions/runs/${{ github.run_id }}"
      - name: Checkout DINAR
        uses: actions/checkout@v2
        with:
          path: DINAR
          repository: itpp-labs/DINAR-fork
          ref: master
      - uses: actions/setup-python@v4
        with:
          python-version: "3.10"
      - name: Install python tools
        run: |
          pip install plumbum pre-commit git-aggregator
      - name: Check Python Version
        run:
          echo "PY=$(python --version --version | sha256sum | cut -d' ' -f1)" >>
          $GITHUB_ENV
      - name: Analyze request
        env:
          TITLE: ${{ github.event.issue.title }}
        run: |
          # sets environment variables that available in next steps via $ {{ env.PORT_... }} notation
          python DINAR/workflow-files/analyze_port_trigger.py "$TITLE"
      - name: Checkout Repo
        uses: actions/checkout@v2
        with:
          path: REPO
          fetch-depth: 0
          ref: ${{ env.PORT_TO_BRANCH }}
      - uses: actions/cache@v4
        with:
          path: ~/.cache/pre-commit
          key: pre-commit|${{ env.PY }}|${{ hashFiles('REPO/.pre-commit-config.yaml') }}
      - name: Copy module to new branch
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Mitchell Admin"
          cd REPO
          if [ ! -d ${{ env.PORT_MODULE }} ]
          then
              # apply original commit history
              if ! git format-patch --keep-subject --stdout origin/${{ env.PORT_TO_BRANCH }}..origin/${{ env.PORT_FROM_BRANCH }} --  ${{ env.PORT_MODULE }} | git am -3 --keep
              then
                  # git am failed
                  git am --abort
              fi
          fi
          if [ ! -d ${{ env.PORT_MODULE }} ]
          then
              # just copy source
              git checkout origin/${{ env.PORT_FROM_BRANCH }} -- ${{ env.PORT_MODULE }}
              git commit -m ":tada:${{ env.PORT_FROM_BRANCH_TAGS }} ${{ env.PORT_MODULE }}
          previous commits history: https://github.com/${{ github.repository }}/commits/${{ env.PORT_FROM_BRANCH }}/${{ env.PORT_MODULE }}
          > Made via .github/workflows/DINAR-PORT.yml"
          fi
      - name: make OCA/odoo-module-migrator
        run: |
          gitaggregate -c DINAR/workflow-files/odoo-module-migrator-mix.yml
          pip install -e ./odoo-module-migrator
      - name: apply OCA/odoo-module-migrator
        run: |
          LOG_FILE=../odoo-module-migrator.logs
          cd REPO
          odoo-module-migrate \
          --modules ${{ env.PORT_MODULE }} \
          --init-version-name ${{ env.PORT_FROM_BRANCH }} \
          --target-version-name ${{ env.PORT_TO_BRANCH }} \
          --no-commit \
          --no-pre-commit \
          2> $LOG_FILE || true
          cat $LOG_FILE

          # remove colors
          sed -r -i "s/\x1B\[([0-9]{1,3}(;[0-9]{1,2})?)?[mGK]//g" $LOG_FILE
          # escape character
          # TODO: update KeisukeYamashita/create-comment to support reading comment's body from file
          echo 'MIGRATOR_LOGS<<EOF' >> $GITHUB_ENV
          cat $LOG_FILE >> $GITHUB_ENV
          echo 'EOF' >> $GITHUB_ENV

          git add -A
          git commit -m ":arrow_up:${{ env.PORT_TO_BRANCH_TAGS }} OCA/odoo-module-migrator

          close #${{ github.event.issue.number }}

          > Made via .github/workflows/DINAR-PORT.yml"
      - name: pre-commit
        run: |
          cd REPO
          pre-commit run --files $(find ${{ env.PORT_MODULE }} -type f) || true
          git add -A
          git commit -m ":rainbow: pre-commit
          > Made via .github/workflows/DINAR-PORT.yml" || echo "pre-commit: no changes"
      - name: PR
        uses: peter-evans/create-pull-request@v3
        id: cpr
        with:
          path: REPO
          # GITHUB_TOKEN would not trigger PR checks
          token: ${{ secrets.DINAR_TOKEN }}
          branch: ${{ env.PORT_TO_BRANCH }}-${{ env.PORT_MODULE }}
          title: "[${{ env.PORT_TO_BRANCH }}] ${{ env.PORT_MODULE }}"
          body: |
            Made by [DINAR](https://github.com/itpp-labs/DINAR#readme) by request in #${{ github.event.issue.number }}
      - name: Post logs
        uses: KeisukeYamashita/create-comment@v1
        with:
          number: ${{ steps.cpr.outputs.pull-request-number }}
          comment: |
            [Migrator](https://github.com/OCA/odoo-module-migrator/)'s [logs](https://github.com/${{ github.repository
            }}/actions/runs/${{ github.run_id }}):

            ```
            ${{ env.MIGRATOR_LOGS }}
            ```
