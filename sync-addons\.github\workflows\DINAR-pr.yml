# Copyright 2020 IT Projects Labs
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: "DINAR"

on:
  pull_request:

jobs:
  pre-commit:
    name: "pre-commit"
    # Let Quick Review/Tests run first
    needs:
      - review
      - tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v2
      - uses: actions/setup-python@v4
        with:
          python-version: "3.10"
      - name: Check Python Version
        run:
          echo "PY=$(python --version --version | sha256sum | cut -d' ' -f1)" >>
          $GITHUB_ENV
      - uses: actions/cache@v4
        with:
          path: ~/.cache/pre-commit
          key: pre-commit|${{ env.PY }}|${{ hashFiles('.pre-commit-config.yaml') }}
      - uses: pre-commit/action@v1.0.1

  review:
    name: "Quick Review"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v2
        with:
          path: REPO
      - name: Checkout DINAR
        uses: actions/checkout@v2
        with:
          path: DINAR
          repository: itpp-labs/DINAR-fork
          ref: master
      - uses: actions/setup-python@v4
        with:
          python-version: "3.10"
      - name: Install python tools
        run: |
          pip install plumbum PyGithub pyyaml
      - name: Analyze PR
        run: |
          # sets environment variables that available in next steps via $ {{ env.PR_... }} notation
          cd REPO
          python ../DINAR/workflow-files/analyze-modules.py updated ${{ secrets.GITHUB_TOKEN }} ${{ github.repository }} ${{ github.event.number }}
      - name: Configure docker
        run: |
          bash DINAR/workflow-files/configure-docker.sh ${{ secrets.DINAR_TOKEN || secrets.GITHUB_TOKEN }}
          echo "PR_FILES=../../REPO" >> $GITHUB_ENV
      - name: HOW TO RUN ODOO LOCALLY
        if: always()
        run: |
          export MODULES=${{ env.PR_MODULES }}
          export LOAD_MODULES=${{ env.PR_MODULES_LOAD }}
          export PR_NUM=${{ github.event.number }}
          export VERSION=${{ github.event.pull_request.base.ref }}
          export REVISION_PR=${{ github.event.pull_request.head.sha}}
          export DINAR_REPO="itpp-labs/DINAR-fork"
          bash DINAR/workflow-files/how-to-run-locally.sh
      - name: Check Python Version
        run:
          echo "PY=$(python --version --version | sha256sum | cut -d' ' -f1)" >>
          $GITHUB_ENV
      - uses: actions/cache@v4
        with:
          path: ~/.cache/pre-commit
          key: pre-commit|${{ env.PY }}|${{ hashFiles('REPO/.pre-commit-config.yaml') }}
      - name: Install pre-commit
        run: |
          pip install pre-commit
      - name: PRE-COMMIT against updated files only
        run: |
          cd REPO
          git fetch origin ${{ github.event.pull_request.base.ref }}
          echo "CHANGED FILES:"
          git diff --name-only --no-ext-diff FETCH_HEAD..HEAD -- .
          echo "RUN PRE-COMMIT:"
          pre-commit run --show-diff-on-failure --color=always --show-diff-on-failure --files $(git diff --name-only --no-ext-diff FETCH_HEAD..HEAD -- .)

  tests:
    name: "Quick Tests"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v2
        with:
          path: REPO
      - name: Checkout DINAR
        uses: actions/checkout@v2
        with:
          path: DINAR
          repository: itpp-labs/DINAR-fork
          ref: master
      - name: Configure docker
        run: |
          bash DINAR/workflow-files/configure-docker.sh ${{ secrets.DINAR_TOKEN || secrets.GITHUB_TOKEN }}
          echo "PR_FILES=../../REPO" >> $GITHUB_ENV
      - name: Install python tools
        run: |
          pip install plumbum PyGithub pyyaml
      - name: Download Docker images with preinstalled modules
        run: |
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml config
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml pull
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml up --no-start
      - name: Analyze PR
        run: |
          # Get list of installed modules
          ODOO_BASE_MODULES=$(docker inspect \
          --format '{{ index .Config.Labels "dinar.odoo.modules"}}' \
          dinar-odoo-1)
          echo "ODOO_BASE_MODULES=$ODOO_BASE_MODULES"

          # sets environment variables that available in next steps via $ {{ env.PR_... }} notation
          cd REPO
          python ../DINAR/workflow-files/analyze-modules.py updated ${{ secrets.GITHUB_TOKEN }} ${{ github.repository }} ${{ github.event.number }} $ODOO_BASE_MODULES
      - name: Install json parser
        run: |
          sudo apt-get install jq
      - name: Install Additional Dependencies (Modules)
        if: env.PR_MODULES_DEPS != ''
        run: |
          # Install new dependencies without tests
          export MODULES="${{ env.PR_MODULES_DEPS }}"
          export LOAD_MODULES="${{ env.PR_MODULES_LOAD }}"
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml config
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml up --abort-on-container-exit
      - name: Install Additional Dependencies (Packages)
        if: env.PR_DEPS != ''
        run: |
          # TODO https://github.com/itpp-labs/DINAR/issues/42
          exit 1
      - name: Prepare Artifact Folder
        run: |
          mkdir new-deps/
          # create dummy file to be sure that Artifact will be uploaded
          echo ok > new-deps/.empty
          echo "ARTIFACT=empty" >> $GITHUB_ENV
      - name: Prepare DINAR with additional dependencies
        if: env.PR_MODULES_DEPS != '' || env.PR_DEPS != ''
        run: |
          # Save artifacts for local run and for integrations Tests
          bash DINAR/workflow-files/save-docker-layers.sh new-deps/
          echo "${{ env.PR_MODULES_DEPS }}" > new-deps/modules.txt
          echo "ARTIFACT=yes" >> $GITHUB_ENV
      - name: Save DINAR with dependencies
        uses: actions/upload-artifact@v4
        with:
          name: new-deps
          path: new-deps/
      - name: HOW TO RUN QUICK TESTS LOCALLY
        if: always()
        run: |
          export MODULES=${{ env.PR_MODULES }}
          export LOAD_MODULES=${{ env.PR_MODULES_LOAD }}
          export PR_NUM=${{ github.event.number }}
          export VERSION=${{ github.event.pull_request.base.ref }}
          export REVISION_PR=${{ github.event.pull_request.head.sha}}
          export DINAR_REPO="itpp-labs/DINAR-fork"
          export ODOO_EXTRA_ARG=--test-enable
          bash DINAR/workflow-files/how-to-run-locally.sh ${{ secrets.GITHUB_TOKEN }}
      - name: Test updated modules
        if: env.PR_MODULES != ''
        run: |
          export MODULES="${{ env.PR_MODULES }}"
          export LOAD_MODULES="${{ env.PR_MODULES_LOAD }}"
          export ODOO_EXTRA_ARG=--test-enable
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml config
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml up --abort-on-container-exit

  tests-all:
    name: Integration Tests
    # Let Quick Review/Tests run first
    # This job uses artifacts from "Quick Tests"
    needs:
      - tests
      - review
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v2
        with:
          path: REPO
      - name: Checkout DINAR
        uses: actions/checkout@v2
        with:
          path: DINAR
          repository: itpp-labs/DINAR-fork
          ref: master
      - name: Install python tools
        run: |
          pip install plumbum pyyaml
      - name: Download Additional Dependencies artifact
        uses: actions/download-artifact@v1
        with:
          name: new-deps
          path: new-deps/
      - name: Check artifact
        run: |
          if [ ! -f new-deps/modules.txt ]; then
              echo "ARTIFACT=empty" >> $GITHUB_ENV
          fi
      - name: Configure Docker
        run: |
          bash DINAR/workflow-files/configure-docker.sh ${{ secrets.DINAR_TOKEN || secrets.GITHUB_TOKEN }}
          echo "PR_FILES=../../REPO" >> $GITHUB_ENV
      - name: Analyze PR
        run: |
          # sets environment variables that available in next steps via $ {{ env.PR_... }} notation
          DEPS_MODULES=$(cat new-deps/modules.txt || true)
          cd REPO
          python ../DINAR/workflow-files/analyze-modules.py all "$DEPS_MODULES"
      - name: Install json parser
        run: |
          sudo apt-get install jq
      - name: HOW TO RUN TESTS LOCALLY
        if: always()
        run: |
          export MODULES=${{ env.ALL_MODULES }}
          export LOAD_MODULES=${{ env.ALL_MODULES_LOAD }}
          export PR_NUM=${{ github.event.number }}
          export VERSION=${{ github.event.pull_request.base.ref }}
          export REVISION_PR=${{ github.event.pull_request.head.sha}}
          export DINAR_REPO="itpp-labs/DINAR-fork"
          export ODOO_EXTRA_ARG=--test-enable
          bash DINAR/workflow-files/how-to-run-locally.sh ${{ secrets.GITHUB_TOKEN }}
      - name: Download base images
        run: |
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml config
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml pull
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml up --no-start
      - name: Apply new-deps artifact
        if: env.ARTIFACT != 'empty'
        run: |
          bash DINAR/workflow-files/load-docker-layers.sh new-deps/
      - name: Test all modules
        run: |
          export MODULES="${{ env.ALL_MODULES }}"
          export LOAD_MODULES="${{ env.ALL_MODULES_LOAD }}"
          export ODOO_EXTRA_ARG=--test-enable
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml config
          docker compose -p dinar -f DINAR/workflow-files/docker-compose-DINAR-pr.yml up --abort-on-container-exit
