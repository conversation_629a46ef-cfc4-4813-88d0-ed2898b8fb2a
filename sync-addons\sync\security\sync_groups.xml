<?xml version="1.0" encoding="UTF-8" ?>
<!-- Copyright 2020 <PERSON> <https://twitter.com/yelizariev>
     License MIT (https://opensource.org/licenses/MIT). -->
<odoo>
    <data>
        <record model="ir.module.category" id="module_category_sync">
            <field name="name">Sync Studio</field>
            <field name="description">
                User: read-only access;
            Developer: restricted write access;
            Administrator: same as Develo<PERSON>, but access to secure staff.
            </field>
        </record>
        <record id="sync_group_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="module_category_sync" />
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]" />
        </record>
        <record id="sync_group_dev" model="res.groups">
            <field name="name">Developer</field>
            <field name="category_id" ref="module_category_sync" />
            <field name="implied_ids" eval="[(4, ref('sync.sync_group_user'))]" />
        </record>
        <record id="sync_group_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="category_id" ref="module_category_sync" />
            <field name="implied_ids" eval="[(4, ref('sync.sync_group_dev'))]" />
        </record>
        <record id="base.group_system" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('sync.sync_group_manager'))]" />
        </record>
    </data>
    <data noupdate="1">
        <record id="base.default_user" model="res.users">
            <field name="groups_id" eval="[(4, ref('sync.sync_group_manager'))]" />
        </record>
    </data>
</odoo>
