<odoo>
    <record id="th_team_tree_view" model="ir.ui.view">
        <field name="name">th_team_tree_view</field>
        <field name="model">th.team</field>
        <field name="arch" type="xml">
            <tree string="<PERSON><PERSON><PERSON> nhóm">
                <field name="name"/>
                <field name="th_manager_id"/>
                <field name="th_user_ids" widget="many2many_tags"/>
            </tree>
        </field>
    </record>

    <record id="th_team_form_view" model="ir.ui.view">
        <field name="name">th.team.form</field>
        <field name="model">th.team</field>
        <field name="arch" type="xml">
            <form string="Đội nhóm">
                <sheet>
                    <group>
                        <field name="th_members_domain" invisible="1"/>
                        <field name="name" required="1"/>
                        <field name="th_manager_id" required="1" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                        <field name="th_user_ids" widget="many2many_tags" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_members_domain"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_team_action" model="ir.actions.act_window">
        <field name="name">Đội nhóm</field>
        <field name="res_model">th.team</field>
        <field name="view_mode">tree,form</field>
    </record>

</odoo>