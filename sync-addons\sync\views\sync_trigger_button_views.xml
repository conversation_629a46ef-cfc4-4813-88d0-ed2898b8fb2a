<?xml version="1.0" encoding="UTF-8" ?>
<!-- Copyright 2020 <PERSON> <https://twitter.com/yelizariev>
     License MIT (https://opensource.org/licenses/MIT). -->
<odoo>
    <record id="sync_trigger_button_view_tree" model="ir.ui.view">
        <field name="name">sync.trigger.button.tree</field>
        <field name="model">sync.trigger.button</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="trigger_name" />
                <field name="sync_project_id" required="1" />
                <field name="sync_task_id" />
            </tree>
        </field>
    </record>
    <record id="sync_trigger_button_view_form" model="ir.ui.view">
        <field name="name">sync.trigger.button.form</field>
        <field name="model">sync.trigger.button</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="trigger_name" />
                    <field name="sync_task_id" />
                </group>
            </form>
        </field>
    </record>
    <record id="sync_trigger_button_action_from_project" model="ir.actions.act_window">
        <field name="name">Button Triggers</field>
        <field name="res_model">sync.trigger.button</field>
        <field name="view_mode">tree</field>
        <field name="domain">[('sync_project_id', '=', active_id)]</field>
    </record>
</odoo>
