<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">
            <h2 class="oe_slogan" style="color:#875A7B;">Sync Studio</h2>
            <h3 class="oe_slogan">Synchronize anything with anything</h3>
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <div class="oe_span12 text-center">
            <p class="oe_mt32">
                Customize your integration right in Odoo backend!
            </p>
        </div>
        <div class="oe_row_img oe_centered">
            <img class="oe_demo oe_picture oe_screenshot" src="task-code.png"/>
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <div class="oe_span12 text-center">
            <p class="oe_mt32">
                Disable part of integration tasks if needed.
            </p>
        </div>
        <div class="oe_row_img oe_centered">
            <img class="oe_demo oe_picture oe_screenshot" src="project-tasks.png"/>
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">

            <div class="alert alert-info oe_mt32" style="padding:0.3em 0.6em; font-size: 150%;">
                <i class="fa fa-hand-o-right"></i><b> Key integration tools: </b>
                <ul class="list-unstyled">

                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        <b>Cron</b>: execute custom code by cron schedule
                    </li>
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        <b>DB Handler</b>: execute custom code on updates in database
                    </li>
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        <b>Incoming Webhook</b>: process data/event send from external system
                    </li>
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        <b>Manual Triggering</b>: execute custom code manually
                    </li>

                </ul>
            </div>

        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">

            <div class="alert alert-success oe_mt32" style="padding:0.3em 0.6em; font-size: 150%;">
                You can check <a href="https://apps.odoo.com/apps/modules/browse?search=sync+studio&repo_maintainer_id=59928">existing solutions</a> based on this module. If there is no module that fits your needs or you need an update, send a request to <a href="mailto:<EMAIL>"><EMAIL></a> <i class="fa fa-envelope-o"></i>.
            </div>

        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">
            <h2>Open Source!</h2>
            <div class="alert alert-danger oe_mt32" style="padding:0.3em 0.6em; font-size: 150%;">
                <p>
                    You can find our open github repository via the <b>Website</b> link in the module description above. You are welcome to make a contribution there! It could be either:
                    <ul>
                        <li>a new module</li>
                        <li>an update to existing module</li>
                        <li>
                            an issue with the data xml file attached (generate one via <em>Action -> Export to XML</em> button in project form)
                        </li>
                        <li>a bug report</li>
                        <li>a feature request</li>
                    </ul>
                </p>
                <p>
                    Your contribution will make other Odoo users happy <span class="fa fa-smile-o"></span>, while <em>IT Projects Labs</em> will assist you on it:
                    <ul>
                        <li>we will test the updates</li>
                        <li>we will check bug reports</li>
                        <li>we will document the modules</li>
                        <li>we will port modules to the new Odoo versions</li>
                    </ul>
                </p>
                <p>
                    Sincerely, Ivan Yelizariev.
                </p>
            </div>

        </div>
    </div>
</section>
