<?xml version="1.0" encoding="UTF-8" ?>
<!-- Copyright 2020,2025 <PERSON> <https://twitter.com/yelizariev>
     License MIT (https://opensource.org/licenses/MIT). -->
<odoo>
    <record id="sync_trigger_automation_view_tree" model="ir.ui.view">
        <field name="name">sync.trigger.automation.tree</field>
        <field name="model">sync.trigger.automation</field>
        <field name="arch" type="xml">
            <tree>
                <field name="trigger_name" />
                <field name="model_id" />
                <field name="trigger" />
                <field name="sync_project_id" />
                <field name="sync_task_id" />
            </tree>
        </field>
    </record>
    <record id="sync_trigger_automation_view_form" model="ir.ui.view">
        <field name="name">sync.trigger.automation.form</field>
        <field name="model">sync.trigger.automation</field>
        <field name="arch" type="xml">
            <form>
                <!--
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_webhook_logs" type="object" string="Logs" class="oe_stat_button" icon="fa-list" invisible="trigger != 'on_webhook'">
                            </button>
                        </div>
                        -->
                <field name="active" invisible="1" />
                <field name="model_name" invisible="1" force_save="True" />
                <widget
                    name="web_ribbon"
                    title="Archived"
                    bg_color="bg-danger"
                    invisible="active"
                />
                <div class="oe_title">
                    <h1>
                        <field name="trigger_name" placeholder="e.g. Support flow" />
                    </h1>
                    <field name="sync_task_id" invisible="1" />
                </div>
                <group
                    groups="!base.group_no_one"
                    invisible="context.get('default_model_id')"
                >
                    <group>
                        <field name="model_id" options="{'no_create': True}" />
                    </group>
                </group>
                <group groups="base.group_no_one">
                    <group>
                        <field name="model_id" options="{'no_create': True}" />
                    </group>
                </group>
                <group invisible="not model_id">
                    <group>
                        <label for="trigger" />
                        <div>
                            <div class="d-flex flex-row">
                                <field
                                    name="trigger"
                                    widget="base_automation_trigger_selection"
                                    class="oe_inline me-3"
                                />
                                <field
                                    name="trg_selection_field_id"
                                    placeholder="Select a value..."
                                    class="oe_inline"
                                    options="{'no_open': True, 'no_create': True}"
                                    invisible="trigger not in ['on_state_set', 'on_priority_set']"
                                    required="trigger in ['on_state_set', 'on_priority_set']"
                                />
                                <field
                                    name="trg_field_ref"
                                    placeholder="Select a value..."
                                    class="oe_inline"
                                    widget="base_automation_trigger_reference"
                                    invisible="trigger not in ['on_stage_set', 'on_tag_set']"
                                    required="trigger in ['on_stage_set', 'on_tag_set']"
                                />
                                <field name="trg_field_ref_model_name" invisible="1" />
                                <field
                                    name="trg_field_ref_display_name"
                                    invisible="1"
                                />
                                <field
                                    name="trg_date_id"
                                    class="oe_inline"
                                    string="Date Field"
                                    options="{'no_open': True, 'no_create': True}"
                                    invisible="trigger != 'on_time'"
                                    required="trigger in ['on_time', 'on_time_created', 'on_time_updated']"
                                />
                            </div>
                            <div class="text-muted" invisible="trigger != 'on_change'">
                                <i class="fa fa-warning" />
                                Automation rules triggered by UI changes will be
                                executed
                                <em>every time</em>
                                the watched fields change,
                                <em>whether you save or not</em>
                                .
                            </div>
                        </div>
                        <!--
                                <label for="url" string="URL" invisible="trigger != 'on_webhook'"/>
                                <div invisible="trigger != 'on_webhook'">
                                    <field name="url" widget="CopyClipboardURL"  placeholder="URL will be created once the rule is saved."/>
                                    <div class="alert alert-warning" role="status">
                                        <strong><i class="fa fa-lock"/> Keep it secret, keep it safe.</strong>
                                        <p>Your webhook URL contains a secret. Don't share it online or carelessly.</p>
                                        <button class="btn btn-seconadry" type="object" name="action_rotate_webhook_uuid" string="Rotate Secret" icon="fa-refresh" help="Change the URL's secret if you think the URL is no longer secure. You will have to update any automated system that calls this webhook to the new URL."/>
                                    </div>
                                </div>
                                -->
                        <label
                            for="trg_date_range"
                            string="Delay"
                            invisible="trigger not in ['on_time', 'on_time_created', 'on_time_updated']"
                        />
                        <div
                            class="d-flex flex-row gap-2"
                            invisible="trigger not in ['on_time', 'on_time_created', 'on_time_updated']"
                        >
                            <field
                                name="trg_date_range"
                                class="oe_inline"
                                required="trigger in ['on_time', 'on_time_created', 'on_time_updated']"
                            />
                            <field
                                name="trg_date_range_type"
                                class="oe_inline"
                                required="trigger in ['on_time', 'on_time_created', 'on_time_updated']"
                            />
                            <span invisible="trigger != 'on_time_created'">
                                after creation
                            </span>
                            <span invisible="trigger != 'on_time_updated'">
                                after last update
                            </span>
                            <span invisible="trigger != 'on_time'">after</span>
                            <field
                                name="trg_date_id"
                                class="oe_inline"
                                string="Date Field"
                                placeholder="Select a date field..."
                                options="{'no_open': True, 'no_create': True}"
                                context="{'hide_model': 1}"
                                invisible="trigger != 'on_time'"
                                required="trigger in ['on_time', 'on_time_created', 'on_time_updated']"
                            />
                        </div>
                        <!--
                                <field name="log_webhook_calls" widget="boolean_toggle" invisible="trigger != 'on_webhook'"/>
                                -->
                        <field
                            name="trg_date_calendar_id"
                            class="oe_inline"
                            options="{'no_open': True, 'no_create': True}"
                            invisible="not trg_date_id or trg_date_range_type != 'day'"
                        />
                        <label
                            for="least_delay_msg"
                            invisible="trigger not in ['on_time', 'on_time_created', 'on_time_updated'] or not least_delay_msg"
                            string=""
                        />
                        <div
                            class="alert alert-info"
                            role="alert"
                            invisible="trigger not in ['on_time', 'on_time_created', 'on_time_updated'] or not least_delay_msg"
                        >
                            <field name="least_delay_msg" />
                        </div>
                        <field
                            name="filter_pre_domain"
                            widget="domain"
                            groups="base.group_no_one"
                            options="{'model': 'model_name', 'in_dialog': True}"
                            invisible="trigger in ['on_change', 'on_webhook']"
                        />
                        <field
                            name="filter_domain"
                            widget="domain"
                            groups="base.group_no_one"
                            options="{'model': 'model_name', 'in_dialog': True}"
                            invisible="trigger in ['on_change', 'on_webhook']"
                        />
                        <label
                            for="filter_domain"
                            groups="!base.group_no_one"
                            invisible="trigger not in ['on_create_or_write', 'on_unlink']"
                        />
                        <label
                            for="filter_domain"
                            groups="!base.group_no_one"
                            string="Extra Conditions"
                            invisible="trigger not in ['on_time', 'on_time_created', 'on_time_updated']"
                        />
                        <field
                            name="filter_domain"
                            nolabel="1"
                            widget="domain"
                            groups="!base.group_no_one"
                            options="{'model': 'model_name', 'in_dialog': False, 'foldable': True}"
                            invisible="trigger not in ['on_create_or_write', 'on_unlink', 'on_time', 'on_time_created', 'on_time_updated']"
                        />
                        <field
                            name="trigger_field_ids"
                            string="When updating"
                            placeholder="Select fields..."
                            options="{'no_open': True, 'no_create': True}"
                            domain="[('model_id', '=', model_id),('store','=',True)]"
                            context="{'hide_model': 1}"
                            invisible="trigger != 'on_create_or_write'"
                            widget="many2many_tags"
                        />
                        <field
                            name="on_change_field_ids"
                            string="When updating"
                            placeholder="Select fields..."
                            options="{'no_open': True, 'no_create': True}"
                            domain="[('model_id', '=', model_id)]"
                            context="{'hide_model': 1}"
                            invisible="trigger != 'on_change'"
                            widget="many2many_tags"
                        />
                    </group>
                    <!--
                            <group>
                                <label for="record_getter" string="Target Record" invisible="trigger != 'on_webhook'" />
                                <div invisible="trigger != 'on_webhook'">
                                    <field name="record_getter" string="Target Record"/>
                                    <div>
                                        <div  class="text-muted"><i class="fa fa-info-circle"/> The default target record getter will work out-of-the-box for any webhook coming from another Odoo instance.</div>
                                        <span class="text-muted"> Available variables: </span>
                                        <ul class="text-muted">
                                            <li><code>env</code>: environment on which the action is triggered</li>
                                            <li><code>model</code>: model of the record on which the action is triggered; is a void recordset</li>
                                            <li><code>time</code>, <code>datetime</code>, <code>dateutil</code>, <code>timezone</code>: useful Python libraries</li>
                                            <li><code>payload</code>: the payload of the call (GET parameters, JSON body), as a dict.</li>
                                        </ul>
                                    </div>
                                </div>
                            </group>
                            -->
                </group>
            </form>
        </field>
    </record>
    <record id="sync_trigger_automation_view_form_full" model="ir.ui.view">
        <field name="name">sync.trigger.automation.form full</field>
        <field name="model">sync.trigger.automation</field>
        <field name="inherit_id" ref="sync_trigger_automation_view_form" />
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='sync_task_id']" position="attributes">
                <attribute name="invisible" />
                <attribute name="required" eval="1" />
            </xpath>
        </field>
    </record>
    <record
        id="sync_trigger_automation_action_from_project"
        model="ir.actions.act_window"
    >
        <field name="name">Automation Triggers</field>
        <field name="res_model">sync.trigger.automation</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('sync_project_id', '=', active_id)]</field>
        <field
            name="view_ids"
            eval="[(5, 0, 0),
                                     (0, 0, {'view_mode': 'tree', 'view_id': ref('sync_trigger_automation_view_tree')}),
                                     (0, 0, {'view_mode': 'form', 'view_id': ref('sync_trigger_automation_view_form_full')})]"
        />
    </record>
</odoo>
