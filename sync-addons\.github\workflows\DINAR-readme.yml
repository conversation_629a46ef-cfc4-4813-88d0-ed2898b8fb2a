# Copyright 2020 IT Projects Labs
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: "DINAR: update repo's readme"

# disabled
on: false

#on:
#  push:
#    paths:
#      - ".DINAR/build-date.txt"
#      - ".DINAR/config.yaml"
#      - ".github/workflows/DINAR-readme.yml"
#      - "*/__manifest__.py"
jobs:
  repo_readme:
    runs-on: ubuntu-latest
    if:
      "! endsWith(github.repository, '-store') && startsWith(github.repository,
      'itpp-labs/')"
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v2
        with:
          path: REPO
          # token is required to bypass pushing without checks/reviews
          token: ${{ secrets.DINAR_TOKEN }}
      - name: Checkout DINAR
        uses: actions/checkout@v2
        with:
          path: DINAR
          repository: itpp-labs/DINAR-fork
          ref: master
      - uses: actions/setup-python@v1
        with:
          python-version: "3.7.x"
      - name: Install python tools
        run: |
          pip install plumbum pyyaml PyGithub
      - name: Generate readme
        run: |
          REF=${GITHUB_BASE_REF:-${GITHUB_REF}}
          BRANCH=${REF##*/}
          cd REPO
          python ../DINAR/workflow-files/generate-repo-readme.py ${{ secrets.GITHUB_TOKEN }} ${{ github.repository }} $BRANCH
      - name: Commit updates
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          repository: REPO
          commit_user_name: Mitchell Admin
          commit_user_email: <EMAIL>
          # Commit may contain other updates, but in usual flow it's only module list.
          commit_message: |
            :construction_worker_man: Update module list

            Sent from Github Actions (see .github/workflows/DINAR-readme.yml )
