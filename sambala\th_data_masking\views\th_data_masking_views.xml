<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Data Masking Strategy Views -->
    <record id="view_data_masking_strategy_tree" model="ir.ui.view">
        <field name="name">th.data.masking.strategy.tree</field>
        <field name="model">th.data.masking.strategy</field>
        <field name="arch" type="xml">
            <tree string="Kỹ thuật mask Dữ liệu">
                <field name="name"/>
                <field name="th_code"/>
                <field name="th_strategy_type"/>
                <field name="th_applicable_field_types"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_data_masking_strategy_form" model="ir.ui.view">
        <field name="name">th.data.masking.strategy.form</field>
        <field name="model">th.data.masking.strategy</field>
        <field name="arch" type="xml">
            <form string="<PERSON><PERSON> thuật mask Dữ liệu">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Tên Kỹ thuật"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="th_code"/>
                            <field name="th_strategy_type"/>
                            <field name="th_applicable_field_types"/>
                        </group>
                        <group>
                            <field name="th_fixed_value" attrs="{'invisible': [('th_strategy_type', '!=', 'fixed')], 'required': [('th_strategy_type', '=', 'fixed')]}"/>
                            <field name="th_pattern" attrs="{'invisible': [('th_strategy_type', '!=', 'pattern')], 'required': [('th_strategy_type', '=', 'pattern')]}"/>
                            <field name="th_partial_keep_start" attrs="{'invisible': [('th_strategy_type', '!=', 'partial')]}"/>
                            <field name="th_partial_keep_end" attrs="{'invisible': [('th_strategy_type', '!=', 'partial')]}"/>
                            <field name="th_monetary_min_value" attrs="{'invisible': [('th_strategy_type', '!=', 'monetary_consistent')]}"/>
                            <field name="th_monetary_max_value" attrs="{'invisible': [('th_strategy_type', '!=', 'monetary_consistent')]}"/>
                            <field name="th_monetary_decimal_places" attrs="{'invisible': [('th_strategy_type', '!=', 'monetary_consistent')]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="description" nolabel="1"/>
                        </page>
                        <page string="Mã Python Tùy chỉnh" name="custom_code" attrs="{'invisible': [('th_strategy_type', '!=', 'custom')]}">
                            <field name="th_custom_python_code" widget="ace" options="{'mode': 'python'}" placeholder="# Biến có sẵn:
                                    # - value: giá trị gốc
                                    # - field_type: loại trường (char, text, integer, v.v.)
                                    # - result: biến sẽ được trả về
                                    # Ví dụ:
                                    # if field_type in ('char', 'text'):
                                    #     result = value[:3] + '***' if value else value
                                    # elif field_type in ('integer', 'float'):
                                    #     result = 0
                                    # else:
                                    #     result = value"/>
                        </page>
                        <page string="Hỗ trợ">
                            <div class="oe_form_group">
                                <h3>Hướng dẫn Kỹ thuật Mask Dữ liệu</h3>

                                <div class="o_group">
                                    <h4>1. Các loại Kỹ thuật:</h4>
                                    <ul>
                                        <li>
                                            <strong>Fixed (Cố định):</strong> Thay thế tất cả giá trị bằng một giá trị cố định</li>
                                        <li>
                                            <strong>Pattern (Mẫu):</strong> Sử dụng ký tự đại diện (* hoặc #) để tạo mẫu mask với * sẽ được thay thế bởi ký tự ngẫu nhiên và # sẽ được thay thế bởi số ngẫu nhiên</li>
                                        <li>
                                            <strong>Partial (Một phần):</strong> Giữ lại một số ký tự đầu/cuối, mask phần còn lại</li>
                                        <li>
                                            <strong>Null (Đặt về Null):</strong> Đặt giá trị về Null</li>
                                        <li>
                                            <strong>Hash (Hàm băm):</strong> Hash giá trị gốc thành một chuỗi ký tự cố định</li>
                                        <li>
                                            <strong>Random (Ngẫu nhiên):</strong> Tạo dữ liệu ngẫu nhiên theo loại trường</li>
                                        <li>
                                            <strong>Custom (Tùy chỉnh):</strong> Sử dụng mã Python để xử lý</li>
                                        <li>
                                            <strong>Monetary Consistent (Tiền tệ Nhất quán):</strong> Mask các trường tiền tệ với tính nhất quán - cùng giá trị gốc sẽ được mask thành cùng giá trị để đảm bảo tính toàn vẹn dữ liệu giữa các bản ghi liên quan</li>
                                    </ul>
                                </div>

                                <div class="o_group">
                                    <h4>2. Quy tắc cho các loại Trường:</h4>
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Loại Trường</th>
                                                <th>Kỹ thuật Phù hợp</th>
                                                <th>Ghi chú</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>char, text</td>
                                                <td>fixed, pattern, partial, custom</td>
                                                <td>Dữ liệu văn bản</td>
                                            </tr>
                                            <tr>
                                                <td>integer, float</td>
                                                <td>fixed,pattern, random, custom</td>
                                                <td>Dữ liệu số</td>
                                            </tr>
                                            <tr>
                                                <td>monetary</td>
                                                <td>fixed, random, monetary_consistent, custom</td>
                                                <td>Dữ liệu tiền tệ - khuyến nghị dùng monetary_consistent</td>
                                            </tr>
                                            <tr>
                                                <td>date, datetime</td>
                                                <td>fixed, random, custom</td>
                                                <td>Ngày tháng</td>
                                            </tr>
                                            <tr>
                                                <td>boolean</td>
                                                <td>fixed,custom</td>
                                                <td>True/False</td>
                                            </tr>
                                            <tr>
                                                <td>selection</td>
                                                <td>fixed, random, custom</td>
                                                <td>Lựa chọn từ danh sách</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="o_group">
                                    <h4>3. Hướng dẫn Mã Python Tùy chỉnh:</h4>
                                    <div class="alert alert-info">
                                        <h5>Biến có sẵn:</h5>
                                        <ul>
                                            <li>
                                                <code>value</code>: Giá trị gốc cần mask</li>
                                            <li>
                                                <code>field_type</code>: Loại trường (char, text, integer, v.v.)</li>
                                            <li>
                                                <code>result</code>: Biến để lưu kết quả (bắt buộc gán)</li>
                                        </ul>
                                    </div>

                                    <h5>Ví dụ mã Python:</h5>
                                    <pre class="bg-light p-3">
                                        # Ví dụ 1: Mask email
                                        if field_type == 'char' and '@' in str(value):
                                            parts = str(value).split('@')
                                            if len(parts) == 2:
                                                username = parts[0]
                                                domain = parts[1]
                                                masked_username = username[:2] + '*' * (len(username) - 2)
                                                result = masked_username + '@' + domain
                                            else:
                                                result = '***@***.com'

                                        # Ví dụ 2: Mask số điện thoại
                                        elif field_type == 'char' and str(value).isdigit():
                                            phone = str(value)
                                            if len(phone) >= 10:
                                                result = phone[:3] + '*' * (len(phone) - 6) + phone[-3:]
                                            else:
                                                result = '*' * len(phone)

                                        # Ví dụ 3: Mask số tiền
                                        elif field_type in ('integer', 'float'):
                                            if value and value > 0:
                                                result = round(float(value) * 0.1, 2)  # Giảm 90%
                                            else:
                                                result = 0

                                        # Ví dụ 4: Mask ngày
                                        elif field_type in ('date', 'datetime'):
                                            import datetime
                                            result = datetime.date(2020, 1, 1)  # Ngày cố định

                                        # Mặc định
                                        else:
                                            result = value</pre>

                                    <div class="alert alert-warning mt-3">
                                        <h5>Lưu ý quan trọng:</h5>
                                        <ul>
                                            <li>Luôn gán giá trị cho biến <code>result</code>
                                            </li>
                                            <li>Kiểm tra <code>value</code> có None không trước khi xử lý</li>
                                            <li>Đảm bảo kiểu dữ liệu trả về phù hợp với trường</li>
                                            <li>Tránh sử dụng các hàm có thể gây lỗi bảo mật</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_data_masking_strategy" model="ir.actions.act_window">
        <field name="name">Kỹ thuật mask</field>
        <field name="res_model">th.data.masking.strategy</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo kỹ thuật mask mới
            </p>
            <p>
                Định nghĩa cách các loại dữ liệu khác nhau sẽ được mask.
            </p>
        </field>
    </record>

    <!-- Data Masking Rule Views -->
    <record id="view_data_masking_rule_tree" model="ir.ui.view">
        <field name="name">th.data.masking.rule.tree</field>
        <field name="model">th.data.masking.rule</field>
        <field name="arch" type="xml">
            <tree string="Quy tắc mask Dữ liệu">
                <field name="name"/>
                <field name="model_id"/>
                <field name="th_apply_to_backups"/>
                <field name="th_apply_to_exports"/>
                <field name="th_scope"/>
                <field name="th_user_ids" widget="many2many_tags"/>
                <field name="th_group_ids" widget="many2many_tags"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_data_masking_rule_form" model="ir.ui.view">
        <field name="name">th.data.masking.rule.form</field>
        <field name="model">th.data.masking.rule</field>
        <field name="arch" type="xml">
            <form string="Quy tắc mask Dữ liệu">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                        <button name="action_view_export_history" type="object" class="oe_stat_button" icon="fa-history" attrs="{'invisible': [('th_apply_to_exports', '=', False)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Lịch sử</span>
                                <span class="o_stat_text">Export</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="model_id" options="{'no_create': True , 'no_open': True}"/>
                            <field name="th_apply_to_exports"/>
                            <field name="th_apply_to_backups"/>
                        </group>
                        <group>
                            <field name="th_domain_user_ids" invisible="1"/>
                            <field name="th_scope" attrs="{'invisible': [('th_apply_to_exports', '=', False)]}"/>
                            <field name="th_group_ids" widget="many2many_tags" options="{'no_create': True , 'no_open': True}" attrs="{'invisible': ['|',('th_apply_to_exports', '=', False),('th_scope', '!=', 'group'),('th_scope','!=','user_in_group')], 'required': [('th_scope', '=', 'group')]}" />
                            <field name="th_user_ids" widget="many2many_tags" options="{'no_create': True, 'no_open': True}" domain="[('id', 'in', th_domain_user_ids)]" attrs="{'invisible': ['|',('th_apply_to_exports', '=', False),'!',('th_scope', 'in', ['user', 'user_in_group'])], 'required': [('th_scope', '=', 'user')]}" />
                        </group>
                        <field name="th_notes"/>
                    </group>
                    <group string="Thông tin Kỹ thuật" groups="base.group_no_one">
                        <group>
                            <field name="th_db_table" readonly="1"/>
                            <!-- <field name="th_db_column" readonly="1"/> -->
                        </group>
                        <group>
                            <field name="model_name" readonly="1"/>
                            <!-- <field name="field_name" readonly="1"/> -->
                        </group>
                        <notebook>
                            <page name="Dòng Quy tắc" string="Dòng Quy tắc">
                                <field name="th_data_masking_rule_line_ids">
                                    <tree editable="bottom">
                                        <field name="th_data_masking_rule_id" invisible="1"/>
                                        <field name="field_id" domain="[('model_id', '=', parent.model_id)]" options="{'no_create': True , 'no_open': True}"/>
                                        <field name="field_type"/>
                                        <field name="th_strategy_id" options="{'no_create': True , 'no_open': True}"/>
                                        <field name="th_is_unique"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_view_data_masking_rule_search" model="ir.ui.view">
        <field name="name">th.data.masking.rule.search</field>
        <field name="model">th.data.masking.rule</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm Quy tắc mask Dữ liệu">
                <field name="name" string="Tên" filter_domain="[('name', 'ilike', self)]"/>
                <field name="model_id" string="Model"/>
                <field name="th_scope" string="Phạm vi"/>
                <field name="th_user_ids" string="Người dùng"/>
                <field name="th_group_ids" string="Nhóm"/>

                <separator/>

                <filter name="filter_apply_to_backups" string="Áp dụng cho Backup" domain="[('th_apply_to_backups', '=', True)]"/>
                <filter name="filter_apply_to_exports" string="Áp dụng cho Export" domain="[('th_apply_to_exports', '=', True)]"/>

                <separator/>

                <filter name="filter_scope_global" string="Áp dụng tất cả" domain="[('th_scope', '=', 'all')]"/>
                <filter name="filter_scope_user" string="Phạm vi Người dùng" domain="[('th_scope', '=', 'user')]"/>
                <filter name="filter_scope_group" string="Phạm vi Nhóm" domain="[('th_scope', '=', 'group')]"/>

                <separator/>

                <filter name="filter_active" string="Hoạt động" domain="[('active', '=', True)]"/>
                <filter name="filter_inactive" string="Không hoạt động" domain="[('active', '=', False)]"/>

                <group expand="0" string="Nhóm theo">
                    <filter name="group_by_model" string="Model" context="{'group_by': 'model_id'}"/>
                    <filter name="group_by_scope" string="Phạm vi" context="{'group_by': 'th_scope'}"/>
                    <filter name="group_by_apply_to_backups" string="Áp dụng cho Backup" context="{'group_by': 'th_apply_to_backups'}"/>
                    <filter name="group_by_apply_to_exports" string="Áp dụng cho Export" context="{'group_by': 'th_apply_to_exports'}"/>
                </group>

                <searchpanel>
                    <field name="model_id" string="Model" icon="fa-database" select="multi" enable_counters="1"/>
                    <field name="th_scope" string="Phạm vi" icon="fa-users" select="multi" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="action_data_masking_rule" model="ir.actions.act_window">
        <field name="name">Quy tắc mask</field>
        <field name="res_model">th.data.masking.rule</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="th_view_data_masking_rule_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo quy tắc mask mới
            </p>
            <p>
                Định nghĩa trường nào sẽ được mask và cách thức.
            </p>
        </field>
    </record>
</odoo>