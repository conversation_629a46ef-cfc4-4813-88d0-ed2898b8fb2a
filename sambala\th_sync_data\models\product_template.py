import json
import xmlrpc.client
from odoo import api, fields, models, _, Command
from odoo.http import request
import json
from odoo.exceptions import ValidationError
import math
import requests
from datetime import datetime
from datetime import timedelta

class ProductTemplate(models.Model):
    _inherit = "product.template"

    video = fields.Char(string='Link youtube', store=True)
    combo_list = fields.One2many('product.combo', 'product_template_id', string="combo List")
    preorder = fields.Datetime("Preorder")
    th_code = fields.Char(string="Mã sản phẩm", readonly=True, copy=False)
    th_e2_product_temp_id = fields.Integer("ID sản phẩm e2",copy=False)

    def _group_expand_states(self, states, domain, order):
        return [key for key, val in type(self).state.selection]
    @api.model
    def get_import_templates(self):
        res = super(ProductTemplate, self).get_import_templates()
        return [{
            'label': _('Import Template for Products'),
            'template': '/th_sync_data/static/template/Product_template.xlsx'
        }]

    @api.constrains('default_code')
    def check_duplicate_default_code(self):
        for record in self:
            default_code_search = self.env['product.template'].search([('default_code', '=', record.default_code)])
            if len(default_code_search) > 1 and record.default_code != False:
                raise ValidationError(_(f"The Internal Reference '{record.default_code}' already exists."))

    @api.model_create_multi
    def create(self, vals_list):
        res = super(ProductTemplate, self).create(vals_list)
        for rec in res:
            apm_module_id = self.env.ref("th_setup_parameters.th_apm_module")
            if not self._context.get('th_test_import', False) and apm_module_id.id in rec.categ_id.th_module_ids.ids and rec.default_code:
                rec.th_get_data_product_apm()
        return res

    def write(self, vals):
        res = super(ProductTemplate, self).write(vals)
        for rec in self:
            apm_module_id = self.env.ref("th_setup_parameters.th_apm_module")
            if not self._context.get('th_test_import', False) and apm_module_id.id in rec.categ_id.th_module_ids.ids and rec.default_code:
                rec.th_get_data_product_apm()
        return res

    def th_get_data_product_apm(self):
        ex_data_product_copy = False
        log = ""
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
                                                      order='id desc')
        ex_data_product = []
        try:
            if not server_api:
                raise ValidationError('Không tìm thấy server!')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password
            for rec in self:
                ex_data_product = {
                    'categ_id': rec.categ_id.th_2e_id if rec.categ_id else False,
                    'product_sambala_id': rec.id,
                    'type': rec.detailed_type,
                    # 'type': rec.type,
                    'default_code': rec.default_code,
                    'description': rec.description if rec.description else '',
                    'name': rec.name,
                    'list_price': rec.list_price,
                    'sale_ok': rec.sale_ok,
                    'purchase_ok': rec.purchase_ok,
                    # 'is_combo': rec.is_combo,
                    # 'combo_product_id': combo_list,
                    'state': "no_public",
                    'preorder': rec.preorder,
                    'is_approved': True,
                    'active': rec.active,
                }
                # combo_list = [(0, 0, {'product_id': combo.product_id.th_e2_product_pro_id,
                #                       'product_quantity': combo.product_quantity,
                #                       'product_combo_sambala_id': combo.id,
                #                       }) for combo in rec.combo_product_id]
                if not rec.th_e2_product_temp_id:
                    # ex_data_product_copy = ex_data_product.copy()
                    # if rec.combo_product_id:
                    #     ex_data_product_copy['combo_product_id'] = combo_list
                    # log += "___bắt đầu tạo___" + str(data_code_2e)
                    data_code_2e = result_apis.execute_kw(db, uid_api, password, 'product.template', 'create', [ex_data_product], {'context': {'create_product_2e': 'True'}})
                    rec.write({'th_e2_product_temp_id': data_code_2e})
                    log += "___Tạo xong___" + str(data_code_2e)
                    # for i in rec.combo_list:
                    #     combo_list_id_value = result_apis.execute_kw(db, uid_api, password, 'product.combo', 'search',
                    #                                                  [[['product_combo_sambala_id', '=', i.id]]])
                    #     i.write({'th_e2_product_combo_id': combo_list_id_value[0]})
                else:
                    data_code_2e = result_apis.execute_kw(db, uid_api, password, 'product.template', 'write', [[rec.th_e2_product_temp_id],ex_data_product], {'context': {'write_product_2e': 'True'}})
                search_default_code_2e = result_apis.execute_kw(db, uid_api, password, 'product.product', 'search',[[['default_code', '=', rec.default_code]]])
                product_pro_id = rec.product_variant_ids.filtered(lambda l:l.default_code==rec.default_code)
                for record in product_pro_id:
                    record.write({'th_e2_product_pro_id': search_default_code_2e[0]})
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e)+log,
                'th_record_id': str(self.ids),
                'th_input_data': str(ex_data_product),
                'th_function_call': str('th_get_data_product_apm'),
            })
        return True

    def synchronized_data_of_apm_product(self):
        product_ids = []
        apm_module_id = self.env.ref("th_setup_parameters.th_apm_module")
        for rec in self.env['product.template'].search([]):
            if apm_module_id.id in rec.categ_id.th_module_ids.ids:
                product_ids.append(rec.id)
        return product_ids

    def call_sync_product_from_e2(self):
        return {
            "products": [
                {"id": prod.id,
                "categ_id": [{"cate_id": prod.categ_id.id, "name": prod.categ_id.name, "cate_e2_id": prod.categ_id.th_2e_id}],
                "type": prod.detailed_type,
                # "type": prod.type,
                "default_code": prod.default_code,
                "description": prod.description,
                "name": prod.name,
                "list_price": prod.list_price,
                "sale_ok": prod.sale_ok,
                "purchase_ok": prod.purchase_ok,
                "is_combo": prod.is_combo,
                "preorder": prod.preorder,
                "combo_product_id": [{"product_quantity": combo.product_quantity, "product_id": combo.product_id.th_e2_product_pro_id} for combo in prod.combo_product_id]
                } for prod in self.search([]).filtered(lambda p: self.env.ref('th_setup_parameters.th_apm_module').id in p.categ_id.th_module_ids.ids)] }

class ProductCategory(models.Model):
    _inherit = 'product.category'

    display_name = fields.Char(string="Display Name")
    th_2e_id = fields.Integer(string="Mã danh múc sản phẩm 2e")
    th_code = fields.Char(string="Mã sản phẩm", readonly=True, copy=False)
    # @api.model_create_multi
    # def create(self, vals_list):
    #     res = super().create(vals_list)
    #     apm_module_id = self.env.ref('th_setup_parameters.th_apm_module').id
    #     for rec in res:
    #         if not self._context.get('th_test_import', False) and apm_module_id in rec.th_module_ids.ids:
    #             self.th_synchronized_product_category(rec, create=True)



    @api.model
    def create(self, values):
        res = super(ProductCategory, self).create(values)
        if not self._context.get('th_test_import', False) and self.env.ref(
                'th_setup_parameters.th_apm_module').id in res.th_module_ids.ids:
            self.th_synchronized_product_category(res, create=True)
        return res

    def write(self, values):
        res = super(ProductCategory, self).write(values)
        if res and not self._context.get('th_test_import', False) and 'name' in values or 'parent_id' in values:
            self.th_synchronized_product_category(self, update=True)
        return res

    def th_synchronized_product_category(self, res, update=None, create=None):
        data_to_send = False
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
                                                      order='id desc')
        try:
            if not server_api:
                raise ValidationError('Không tìm thấy server!')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password

            for rec in res:
                data_to_send = {
                    'name': rec.name,
                    'parent_id': rec.parent_id.th_2e_id if rec.parent_id else False,
                }
                if create:
                    category_id = result_apis.execute_kw(db, uid_api, password, 'product.category', 'create',
                                                         [data_to_send])
                    rec.write({'th_2e_id': category_id})
                elif update:
                    if self.env.ref('th_setup_parameters.th_apm_module').id in res.th_module_ids.ids and rec.th_2e_id:
                        result_apis.execute_kw(db, uid_api, password, 'product.category', 'write',
                                               [[rec.th_2e_id], data_to_send])
                    elif self.env.ref(
                            'th_setup_parameters.th_apm_module').id in res.th_module_ids.ids and not rec.th_2e_id:
                        category_id = result_apis.execute_kw(db, uid_api, password, 'product.category', 'create',
                                                             [data_to_send])
                        rec.write({'th_2e_id': category_id})
                    elif not self.env.ref(
                            'th_setup_parameters.th_apm_module').id in res.th_module_ids.ids and rec.th_2e_id:
                        result_apis.execute_kw(db, uid_api, password, 'product.category', 'unlink', [[rec.th_2e_id]])
                        rec.th_2e_id = False
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(res.ids),
                'th_input_data': str(data_to_send),
                'th_function_call': str('th_synchronized_product_category'),
            })
            return

    def name_get(self):
        result = []
        for record in self:
            try:
                if record.parent_id.display_name:
                    record.display_name = record.parent_id.display_name + ' / ' + record.name
                else:
                    record.display_name = record.name
                result.append((record.id, record.display_name))
            except:
                record.display_name = record.name
                result.append((record.id, record.display_name))
        return result

class ProductProduct(models.Model):
    _inherit = 'product.product'

    th_e2_product_pro_id = fields.Integer("ID biến thể sản phẩm e2", copy=False)

class ProductCombo(models.Model):
    _inherit = 'product.combo'

    th_e2_product_combo_id = fields.Integer("ID combo sản phẩm e2", copy=False)

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        # if res.product_template_id.th_e2_product_temp_id and not self._context.get('th_test_import', False):
        #     res.th_synchronized_product_combo(res,create=True)
        return res

    def write(self, values):
        # Add code here
        res = super(ProductCombo, self).write(values)
        # for rec in self:
        #     if rec.th_e2_product_combo_id and not self._context.get('th_test_import', False):
        #         rec.th_synchronized_product_combo(rec,update=True)
        return res

    def th_synchronized_product_combo(self, records, update=None, create=None):
        combo_list_exist = False
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
                                                      order='id desc')
        try:
            if not server_api:
                raise ValidationError('Không tìm thấy server!')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password
            for rec in records:
                combo_list_exist = {'product_id': rec.product_id.th_e2_product_pro_id if rec.product_id else False,
                                    'product_quantity': rec.product_quantity,
                                    'product_combo_sambala_id': rec.id,
                                    'product_template_id': rec.product_template_id.th_e2_product_temp_id if rec.product_template_id else False,}
                if update:
                    result_apis.execute_kw(db, uid_api, password, 'product.combo', 'write',
                                           [[rec.th_e2_product_combo_id], combo_list_exist])
                if create:
                    new_combo_id = result_apis.execute_kw(db, uid_api, password, 'product.combo', 'create',
                                           [combo_list_exist])
                    rec.write({'th_e2_product_combo_id': new_combo_id})
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(records.id),
                'th_input_data': str(combo_list_exist),
                'th_function_call': str('th_synchronized_product_combo'),
            })
            return

class ProductTags(models.Model):
    _inherit = 'product.tag'

    th_2e_id = fields.Integer(string="Mã danh múc sản phẩm 2e")

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        for rec in res:
            rec.th_synchronized_product_tags()
        return res

    def th_synchronized_product_tags(self):
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1, order='id desc')
        try:
            if not server_api:
                raise ValidationError('Không tìm thấy server!')

            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password
            for rec in self:
                product_tag_id = result_apis.execute_kw(db, uid_api, password, 'product.tag', 'create', [{'name': rec.name}])
                rec.write({'th_2e_id': product_tag_id})
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(self.ids),
                'th_input_data': str({'name': 'rec.name'}),
                'th_function_call': str('th_synchronized_product_tags'),
            })
            return

